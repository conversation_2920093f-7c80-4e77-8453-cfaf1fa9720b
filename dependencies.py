"""
FastAPI dependencies for authentication and process management.
"""
import os
import logging
from typing import Optional, Annotated
from fastapi import Depends, HTTPException, Request, Form, status
from fastapi.security import HTTPBearer
from models import ProcessStatus, AppConfig

# Global state management
process_status = ProcessStatus()
security = HTTPBearer(auto_error=False)

# Environment mode
mode = os.getenv('ENVIRONMENT') == 'development'


async def get_iap_authentication(request: Request) -> Optional[str]:
    """
    Dependency for IAP (Identity-Aware Proxy) authentication.
    
    Args:
        request: FastAPI request object
        
    Returns:
        JWT assertion if authenticated
        
    Raises:
        HTTPException: If authentication fails
    """
    if mode:
        # Skip authentication in development mode
        return "development-mode"
    
    jwt_assertion = request.headers.get('X-Goog-Iap-Jwt-Assertion')
    if not jwt_assertion:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="You are not authorized by IAP."
        )
    
    return jwt_assertion


async def get_process_management(
    request: Request,
    uuid: Annotated[str, Form()] = None,
    auth: str = Depends(get_iap_authentication)
) -> ProcessStatus:
    """
    Dependency for process management and UUID tracking.
    
    Args:
        request: FastAPI request object
        uuid: Process UUID from form data
        auth: Authentication dependency
        
    Returns:
        Current process status
        
    Raises:
        HTTPException: If process is busy
    """
    global process_status
    
    # Check if this is a multipart form request with UUID
    content_type = request.headers.get('content-type', '')
    if request.method == 'POST' and "multipart/form-data" in content_type and uuid:
        if process_status.process_id:
            raise HTTPException(
                status_code=status.HTTP_205_RESET_CONTENT,
                detail="Process is busy"
            )
        else:
            process_status.process_id = uuid
            logging.info(f"Started process with ID: {uuid}")
    
    return process_status


async def get_app_config() -> AppConfig:
    """
    Dependency to get current application configuration.
    
    Returns:
        Current app configuration
    """
    return process_status.config


def clear_process_config(function: str, status: str) -> None:
    """
    Clear and reset process configuration.
    
    Args:
        function: Function name being executed
        status: Current status
    """
    global process_status
    
    if function not in ['inventory', 'kube', 'stratozone', 'compliance', 'glide', '']:
        import time
        time.sleep(3)
    
    process_status.config = AppConfig(
        gcloudcmd="",
        file_path="",
        error_m2vm="",
        erc=1,
        status=status,
        function=function,
        msg=""
    )


def update_process_config(**kwargs) -> None:
    """
    Update process configuration with provided values.
    
    Args:
        **kwargs: Configuration values to update
    """
    global process_status
    
    for key, value in kwargs.items():
        if hasattr(process_status.config, key):
            setattr(process_status.config, key, value)


def reset_process_id() -> None:
    """Reset the global process ID."""
    global process_status
    process_status.process_id = None
    logging.info("Process ID reset")


def get_current_process_id() -> Optional[str]:
    """Get the current process ID."""
    return process_status.process_id
