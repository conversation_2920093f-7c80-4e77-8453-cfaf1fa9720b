"""
FastAPI application converted from Flask.
Production-ready migration and processing service.
"""
import os
import sys
import logging
import time
import asyncio
from typing import Optional, Dict, Any, List
from pathlib import Path
from contextlib import asynccontextmanager

from fastapi import (
    FastAPI,
    Depends,
    HTTPException,
    Request,
    Form,
    File,
    UploadFile,
    Query,
    status
)
from fastapi.responses import (
    JSONResponse,
    FileResponse,
    HTMLResponse,
    PlainTextResponse
)
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from ansi2html import Ansi2HTMLConverter

# Import internal modules
from inventory.inventory import Inventory
from kubernetes_inventory.kubernetes import Kubernetes
from migration.buildsheet import buildsheet1
from migration.buildvms import gcloudcmds1
from migration.json_to_excel import json_to_exl
from migration.m2vm import handle_m2vm_request
from migration.move_vali import move_vali
from migration.vmware_excel import vmwarem2vm
from migration.gta_m2vm import gta_m2vm1
from migration.aws_bill import process_aws_bill
from compliance.process import Compliance
from stratozone.stratozone import Stratozone
from glide.init import glide
from video.excel import create_list_of_dictionaries_from_csv

# Import FastAPI modules
from models import (
    ProcessResponse, 
    BusyResponse, 
    LogResponse,
    VideoDataResponse,
    AppConfig
)
from dependencies import (
    get_iap_authentication,
    get_process_management,
    get_app_config,
    clear_process_config,
    update_process_config,
    reset_process_id,
    get_current_process_id,
    process_status
)
from exception_handlers import (
    general_exception_handler,
    http_exception_handler,
    validation_exception_handler
)
from middleware import LoggingMiddleware, setup_logging

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logging.info("FastAPI application starting up...")

    # Create necessary directories
    os.makedirs("templates", exist_ok=True)
    os.makedirs("static", exist_ok=True)

    logging.info("FastAPI application started successfully")

    yield

    # Shutdown
    logging.info("FastAPI application shutting down...")
    reset_process_id()
    logging.info("FastAPI application shutdown complete")


# Initialize FastAPI app
app = FastAPI(
    title="Hypatia Migration Service",
    description="Production-ready migration and processing service",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Setup logging
setup_logging()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add custom logging middleware
app.add_middleware(LoggingMiddleware)

# Setup templates and static files
templates = Jinja2Templates(directory="templates")

# Mount static files if directory exists
if Path("static").exists():
    app.mount("/static", StaticFiles(directory="static"), name="static")

# Global variables
converter = Ansi2HTMLConverter()
BUSY_RESPONSE = BusyResponse()

# Exception handlers
app.add_exception_handler(Exception, general_exception_handler)
app.add_exception_handler(HTTPException, http_exception_handler)


async def get_log_content(log_type: str) -> str:
    """
    Get log content from files.

    Args:
        log_type: Type of log to retrieve ('server' or 'process')

    Returns:
        Log content as string
    """
    try:
        import aiofiles

        if log_type == "server":
            async with aiofiles.open("app.log", "r") as f:
                content = await f.read()
                return content
        else:
            async with aiofiles.open("templates/log.html", "r") as f:
                content = await f.read()
                return content
    except FileNotFoundError:
        return "Log file not found!"
    except Exception as e:
        logging.error(f"Error reading log file: {str(e)}")
        return f"Error reading log file: {str(e)}"


async def process_request_async(
    func: callable,
    function_name: str,
    request: Request,
    root_path: str = "."
) -> FileResponse | PlainTextResponse:
    """
    Process requests asynchronously with error handling.
    
    Args:
        func: Function to execute
        function_name: Name of the function for tracking
        request: FastAPI request object
        root_path: Application root path
        
    Returns:
        File response or error response
    """
    try:
        clear_process_config(function_name, "processing")
        
        # Execute the function (these are synchronous functions from the original modules)
        file, err, msg, err_code = await asyncio.get_event_loop().run_in_executor(
            None, func, request, root_path
        )
        
        update_process_config(
            file_path=file,
            error_m2vm=err,
            erc=err_code,
            msg=msg,
            status="Completed"
        )
        
        logging.info(f"Process completed: {get_current_process_id()}")
        reset_process_id()
        
        if err_code in [1, 2]:
            return PlainTextResponse(
                content=f"{msg}\n{err}",
                status_code=status.HTTP_205_RESET_CONTENT
            )
        else:
            return FileResponse(
                path=file,
                filename=os.path.basename(file),
                media_type='application/octet-stream'
            )
            
    except Exception as e:
        logging.error(f"Error in process_request_async: {str(e)}", exc_info=True)
        reset_process_id()
        update_process_config(
            msg="Internal error!",
            erc=0,
            status="Error"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "hypatia-migration-service"}


# Process status endpoint
@app.get("/processd/{pid}", response_model=ProcessResponse)
async def get_process_status(
    pid: str,
    auth: str = Depends(get_iap_authentication)
) -> ProcessResponse:
    """
    Get process status and results.
    
    Args:
        pid: Process ID to check
        auth: Authentication dependency
        
    Returns:
        Process status and results
    """
    global BUSY_RESPONSE
    
    current_pid = get_current_process_id()
    
    # Wait for process to complete
    if not current_pid:
        for _ in range(3):
            if current_pid:
                break
            await asyncio.sleep(1)
        else:
            return BUSY_RESPONSE
    
    if pid != current_pid:
        return BUSY_RESPONSE
    
    # Wait for process completion
    while pid == get_current_process_id():
        await asyncio.sleep(1)
    
    logging.info(f"Process status requested for: {pid}")
    
    config = process_status.config
    return ProcessResponse(
        msg=config.msg,
        erc=config.erc,
        cmd=config.gcloudcmd,
        path=config.file_path
    )


# Migration and processing endpoints
@app.post("/move_val")
async def move_val_endpoint(
    request: Request,
    uuid: str = Form(...),
    file: UploadFile = File(...),
    process_mgmt: Any = Depends(get_process_management)
):
    """Move validation endpoint."""
    return await process_request_async(move_vali, "move_val", request)


@app.post("/m2vm")
async def m2vm_endpoint(
    request: Request,
    uuid: str = Form(...),
    file: UploadFile = File(...),
    process_mgmt: Any = Depends(get_process_management)
):
    """M2VM migration endpoint."""
    return await process_request_async(handle_m2vm_request, "handle_m2vm_request", request)


@app.post("/gta_m2vm")
async def gta_m2vm_endpoint(
    request: Request,
    uuid: str = Form(...),
    file: UploadFile = File(...),
    process_mgmt: Any = Depends(get_process_management)
):
    """GTA M2VM migration endpoint."""
    return await process_request_async(gta_m2vm1, "gta_m2vm", request)


@app.post("/vmware_m2vm")
async def vmware_m2vm_endpoint(
    request: Request,
    uuid: str = Form(...),
    file: UploadFile = File(...),
    process_mgmt: Any = Depends(get_process_management)
):
    """VMware M2VM migration endpoint."""
    return await process_request_async(vmwarem2vm, "vmware_m2vm", request)


@app.post("/aws_export")
async def aws_export_endpoint(
    request: Request,
    uuid: str = Form(...),
    file: UploadFile = File(...),
    process_mgmt: Any = Depends(get_process_management)
):
    """AWS export endpoint."""
    return await process_request_async(process_aws_bill, "process_aws_bill", request)


@app.post("/buildsheet")
async def buildsheet_endpoint(
    request: Request,
    uuid: str = Form(...),
    file: UploadFile = File(...),
    process_mgmt: Any = Depends(get_process_management)
):
    """Buildsheet generation endpoint."""
    return await process_request_async(buildsheet1, "buildsheet", request)


@app.post("/gcloudcmd")
async def gcloudcmd_endpoint(
    request: Request,
    uuid: str = Form(...),
    file: UploadFile = File(...),
    process_mgmt: Any = Depends(get_process_management)
):
    """Google Cloud command generation endpoint."""
    return await process_request_async(gcloudcmds1, "gcloudcmd", request)


@app.post("/json_to_xl")
async def json_to_xl_endpoint(
    request: Request,
    uuid: str = Form(...),
    file: UploadFile = File(...),
    process_mgmt: Any = Depends(get_process_management)
):
    """JSON to Excel conversion endpoint."""
    return await process_request_async(json_to_exl, "json_to_xl", request)


@app.post("/compliance")
async def compliance_endpoint(
    request: Request,
    uuid: str = Form(...),
    file: UploadFile = File(...),
    process_mgmt: Any = Depends(get_process_management)
):
    """Compliance processing endpoint."""
    return await process_request_async(Compliance, "compliance", request)


@app.post("/stratozone")
async def stratozone_endpoint(
    request: Request,
    uuid: str = Form(...),
    file: UploadFile = File(...),
    process_mgmt: Any = Depends(get_process_management)
):
    """Stratozone processing endpoint."""
    return await process_request_async(Stratozone, "stratozone", request)


@app.post("/inventory")
async def inventory_endpoint(
    request: Request,
    uuid: str = Form(...),
    file: UploadFile = File(...),
    process_mgmt: Any = Depends(get_process_management)
):
    """Inventory processing endpoint."""
    return await process_request_async(Inventory, "inventory", request)


@app.post("/glide")
async def glide_endpoint(
    request: Request,
    uuid: str = Form(...),
    file: UploadFile = File(...),
    process_mgmt: Any = Depends(get_process_management)
):
    """Glide processing endpoint."""
    return await process_request_async(glide, "glide", request)


@app.post("/kube")
async def kube_endpoint(
    request: Request,
    uuid: str = Form(...),
    file: UploadFile = File(...),
    process_mgmt: Any = Depends(get_process_management)
):
    """Kubernetes processing endpoint."""
    return await process_request_async(Kubernetes, "kube", request)


# File download endpoint
@app.get("/dfile")
async def download_file(
    path: str = Query(..., description="File path to download"),
    auth: str = Depends(get_iap_authentication)
):
    """
    Download file endpoint.

    Args:
        path: File path to download
        auth: Authentication dependency

    Returns:
        File response or error message
    """
    if not path or not os.path.exists(path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Error: Invalid file path or file not found."
        )

    return FileResponse(
        path=path,
        filename=os.path.basename(path),
        media_type='application/octet-stream'
    )


# Template rendering endpoints
@app.get("/logs", response_class=HTMLResponse)
async def logs_page(
    request: Request,
    auth: str = Depends(get_iap_authentication)
):
    """Logs page endpoint."""
    return templates.TemplateResponse("log.html", {"request": request})


@app.get("/logging", response_class=HTMLResponse)
async def log_view(
    request: Request,
    auth: str = Depends(get_iap_authentication)
):
    """Logging view endpoint."""
    server_content = await get_log_content("server")
    process_content = await get_log_content("process")

    return templates.TemplateResponse(
        "logging.html",
        {
            "request": request,
            "server_content": server_content,
            "process_content": process_content
        }
    )


@app.get("/video", response_class=HTMLResponse)
async def video_page(
    request: Request,
    auth: str = Depends(get_iap_authentication)
):
    """Video page endpoint."""
    return templates.TemplateResponse("video.html", {"request": request})


@app.get("/video/data", response_model=VideoDataResponse)
async def video_data(
    auth: str = Depends(get_iap_authentication)
):
    """Video data endpoint."""
    try:
        data = await asyncio.get_event_loop().run_in_executor(
            None, create_list_of_dictionaries_from_csv, 'video/data1.csv'
        )
        return VideoDataResponse(data=data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error loading video data: {str(e)}"
        )


@app.get("/", response_class=HTMLResponse)
async def home(
    request: Request,
    auth: str = Depends(get_iap_authentication)
):
    """Home page endpoint."""
    return templates.TemplateResponse("index.html", {"request": request})


# Lifespan events are now handled in the lifespan context manager above


if __name__ == '__main__':
    import uvicorn

    # Get environment configuration
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', 8080))
    debug = os.getenv('ENVIRONMENT') == 'development'

    # Run the application
    uvicorn.run(
        "main_fastapi:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info" if not debug else "debug",
        access_log=True
    )
