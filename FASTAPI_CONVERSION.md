# Flask to FastAPI Conversion

This document describes the conversion of the Flask application (`main.py`) to a production-ready FastAPI application (`main_fastapi.py`).

## Overview

The original Flask application has been successfully converted to FastAPI while maintaining all existing functionality and adding modern features for production deployment.

## Key Changes

### 1. Framework Migration
- **Flask** → **FastAPI**
- Route decorators converted from `@app.route()` to FastAPI path operations
- Request handling converted from Flask's `request` object to FastAPI's dependency injection
- Response handling updated to use FastAPI's response models

### 2. Authentication
- Flask's `@app.before_request` converted to FastAPI dependency injection
- IAP (Identity-Aware Proxy) authentication implemented as reusable dependency
- Proper HTTP status codes and error responses

### 3. File Upload Handling
- Flask's multipart form handling converted to FastAPI's `UploadFile`
- Proper async file handling with `aiofiles`
- Type validation with Pydantic models

### 4. Process Management
- Global `PROCESS_ID` tracking maintained with proper async handling
- Process status tracking with structured data models
- Improved error handling and logging

### 5. Error Handling
- <PERSON><PERSON><PERSON>'s error decorator replaced with FastAPI exception handlers
- Structured error responses with proper HTTP status codes
- Global exception handling for unhandled errors

### 6. CORS & Middleware
- Flask-CORS replaced with FastAPI's `CORSMiddleware`
- Custom logging middleware for request/response logging
- Modern lifespan event handling

### 7. Template Rendering
- Flask's `render_template` replaced with FastAPI's `Jinja2Templates`
- Static file serving with `StaticFiles`
- Async template rendering

### 8. Dependencies
- Added FastAPI-specific dependencies:
  - `python-multipart` for file uploads
  - `jinja2` for template rendering
  - `aiofiles` for async file operations
  - `ansi2html` (existing dependency)

## File Structure

```
├── main.py                 # Original Flask application
├── main_fastapi.py         # New FastAPI application
├── models.py               # Pydantic models for request/response validation
├── dependencies.py         # FastAPI dependencies for auth and process management
├── exception_handlers.py   # FastAPI exception handlers
├── middleware.py           # Custom middleware for logging
├── run_fastapi.py          # Startup script
├── pyproject.toml          # Updated dependencies
└── FASTAPI_CONVERSION.md   # This documentation
```

## Installation

1. Install dependencies:
```bash
uv sync
```

2. Ensure all required directories exist:
```bash
mkdir -p templates static
```

## Running the Application

### Development Mode
```bash
ENVIRONMENT=development python run_fastapi.py
```

### Production Mode
```bash
python run_fastapi.py
```

### Using uvicorn directly
```bash
uvicorn main_fastapi:app --host 0.0.0.0 --port 8080 --reload
```

## API Documentation

FastAPI automatically generates interactive API documentation:

- **Swagger UI**: http://localhost:8080/docs
- **ReDoc**: http://localhost:8080/redoc

## Endpoints

All original Flask endpoints have been preserved:

### Migration Endpoints (POST)
- `/move_val` - Move validation
- `/m2vm` - M2VM migration
- `/gta_m2vm` - GTA M2VM migration
- `/vmware_m2vm` - VMware M2VM migration
- `/aws_export` - AWS export
- `/buildsheet` - Buildsheet generation
- `/gcloudcmd` - Google Cloud command generation
- `/json_to_xl` - JSON to Excel conversion
- `/compliance` - Compliance processing
- `/stratozone` - Stratozone processing
- `/inventory` - Inventory processing
- `/glide` - Glide processing
- `/kube` - Kubernetes processing

### Utility Endpoints
- `GET /processd/{pid}` - Get process status
- `GET /dfile?path=<file_path>` - Download file
- `GET /health` - Health check

### Template Endpoints
- `GET /` - Home page
- `GET /logs` - Logs page
- `GET /logging` - Logging view
- `GET /video` - Video page
- `GET /video/data` - Video data

## Features

### 1. Async Support
- Proper async/await patterns where appropriate
- Non-blocking file operations
- Concurrent request handling

### 2. Type Safety
- Pydantic models for request/response validation
- Type hints throughout the codebase
- Automatic data validation

### 3. Authentication
- IAP authentication as dependency
- Configurable for development/production
- Proper error handling for unauthorized requests

### 4. Process Management
- Global process tracking with UUID
- Busy state management
- Process status monitoring

### 5. Error Handling
- Structured error responses
- Proper HTTP status codes
- Comprehensive logging

### 6. Production Ready
- Multiple worker support
- Proper logging configuration
- Health check endpoint
- Environment-based configuration

## Environment Variables

- `ENVIRONMENT` - Set to 'development' for dev mode
- `HOST` - Server host (default: 0.0.0.0)
- `PORT` - Server port (default: 8080)
- `SECRET_KEY` - Application secret key

## Migration Notes

1. **Backward Compatibility**: All existing endpoints maintain the same URLs and functionality
2. **File Uploads**: Multipart form data handling is preserved
3. **Authentication**: IAP authentication logic is maintained
4. **Process Tracking**: Global process ID tracking works the same way
5. **Templates**: Template rendering requires the `templates/` directory
6. **Static Files**: Static file serving requires the `static/` directory

## Testing

The FastAPI application can be tested using the same client code as the Flask application, as all endpoints maintain backward compatibility.

## Performance Benefits

- **Async Support**: Better handling of I/O-bound operations
- **Type Validation**: Automatic request/response validation
- **Documentation**: Auto-generated API documentation
- **Modern Framework**: Better performance and developer experience
- **Production Ready**: Built-in support for multiple workers and deployment

## Next Steps

1. Install and test the FastAPI application
2. Migrate any custom templates to the `templates/` directory
3. Configure static files in the `static/` directory
4. Update deployment scripts to use the new FastAPI application
5. Consider adding additional FastAPI features like background tasks, WebSocket support, etc.
