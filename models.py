"""
Pydantic models for FastAPI request/response validation.
"""
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field


class ProcessResponse(BaseModel):
    """Response model for process status."""
    msg: str
    erc: int
    cmd: Optional[str] = ""
    path: Optional[str] = ""


class BusyResponse(BaseModel):
    """Response model when app is busy."""
    msg: str = "App is busy, please wait a few minutes"
    erc: int = 0


class ErrorResponse(BaseModel):
    """Response model for errors."""
    detail: str
    status_code: int


class FileUploadRequest(BaseModel):
    """Base model for file upload requests."""
    uuid: str = Field(..., description="Process UUID for tracking")


class LogResponse(BaseModel):
    """Response model for log content."""
    content: str


class VideoDataResponse(BaseModel):
    """Response model for video data."""
    data: List[Dict[str, Any]]


class AppConfig(BaseModel):
    """Application configuration model."""
    gcloudcmd: str = ""
    file_path: str = ""
    error_m2vm: str = ""
    erc: int = 1
    status: str = "Completed"
    function: str = ""
    msg: str = ""
    pids: List[str] = []


class ProcessStatus(BaseModel):
    """Process status tracking model."""
    process_id: Optional[str] = None
    status: str = "idle"
    function: str = ""
    config: AppConfig = AppConfig()
