#!/usr/bin/env python3
"""
Simple test script to verify FastAPI application functionality.
"""
import os
import sys
import asyncio
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent))

async def test_basic_functionality():
    """Test basic FastAPI application functionality."""
    print("Testing FastAPI application...")
    
    try:
        # Test imports
        print("1. Testing imports...")
        from models import ProcessResponse, BusyResponse
        from dependencies import get_current_process_id, reset_process_id
        from middleware import setup_logging
        print("   ✓ All modules imported successfully")
        
        # Test models
        print("2. Testing Pydantic models...")
        response = ProcessResponse(msg="test", erc=0, cmd="test", path="/test")
        busy = BusyResponse()
        print(f"   ✓ ProcessResponse: {response.msg}")
        print(f"   ✓ BusyResponse: {busy.msg}")
        
        # Test process management
        print("3. Testing process management...")
        current_pid = get_current_process_id()
        print(f"   ✓ Current process ID: {current_pid}")
        reset_process_id()
        print("   ✓ Process ID reset")
        
        # Test logging setup
        print("4. Testing logging setup...")
        setup_logging()
        print("   ✓ Logging configured")
        
        # Test FastAPI app creation (without running)
        print("5. Testing FastAPI app creation...")
        
        # Set environment for testing
        os.environ['ENVIRONMENT'] = 'development'
        
        # Import the app (this will test the lifespan context manager)
        from main_fastapi import app
        print(f"   ✓ FastAPI app created: {app.title}")
        print(f"   ✓ App version: {app.version}")
        
        print("\n✅ All tests passed! FastAPI application is ready.")
        return True
        
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        print("   Make sure all required modules are available.")
        return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

async def test_endpoints_structure():
    """Test that all endpoints are properly defined."""
    print("\nTesting endpoint structure...")
    
    try:
        from main_fastapi import app
        
        # Get all routes
        routes = []
        for route in app.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                routes.append((route.path, list(route.methods)))
        
        print(f"Found {len(routes)} routes:")
        for path, methods in sorted(routes):
            print(f"   {', '.join(methods):8} {path}")
        
        # Check for key endpoints
        expected_endpoints = [
            '/health',
            '/processd/{pid}',
            '/move_val',
            '/m2vm',
            '/gta_m2vm',
            '/vmware_m2vm',
            '/aws_export',
            '/buildsheet',
            '/gcloudcmd',
            '/json_to_xl',
            '/compliance',
            '/stratozone',
            '/inventory',
            '/glide',
            '/kube',
            '/dfile',
            '/logs',
            '/logging',
            '/video',
            '/video/data',
            '/'
        ]
        
        found_paths = [path for path, _ in routes]
        missing = [ep for ep in expected_endpoints if ep not in found_paths]
        
        if missing:
            print(f"\n❌ Missing endpoints: {missing}")
            return False
        else:
            print("\n✅ All expected endpoints found!")
            return True
            
    except Exception as e:
        print(f"❌ Error testing endpoints: {e}")
        return False

if __name__ == '__main__':
    async def main():
        success1 = await test_basic_functionality()
        success2 = await test_endpoints_structure()
        
        if success1 and success2:
            print("\n🎉 FastAPI conversion successful!")
            print("\nNext steps:")
            print("1. Create templates/ directory with your HTML templates")
            print("2. Create static/ directory for static files")
            print("3. Run the application with: python run_fastapi.py")
            print("4. Access API docs at: http://localhost:8080/docs")
        else:
            print("\n❌ Some tests failed. Please check the errors above.")
            sys.exit(1)
    
    asyncio.run(main())
