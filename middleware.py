"""
FastAPI middleware for logging and request processing.
"""
import logging
import time
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware


class NoStaticFilter(logging.Filter):
    """Filter to remove static file logs."""
    
    def filter(self, record):
        return "GET /static/" not in record.getMessage()


class LoggingMiddleware(BaseHTTPMiddleware):
    """Custom logging middleware for FastAPI."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request and add logging.
        
        Args:
            request: FastAPI request object
            call_next: Next middleware/endpoint in chain
            
        Returns:
            Response from the endpoint
        """
        start_time = time.time()
        
        # Log request
        logging.info(f"{request.method} {request.url.path}")
        
        # Process request
        response = await call_next(request)
        
        # Log response time
        process_time = time.time() - start_time
        logging.info(f"Request processed in {process_time:.4f}s")
        
        return response


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        filename="app.log",
        level=logging.INFO,
        format="[%(asctime)s] %(levelname)s: %(message)s"
    )
    
    # Add filter to remove static file logs
    uvicorn_logger = logging.getLogger("uvicorn.access")
    uvicorn_logger.addFilter(NoStaticFilter())
