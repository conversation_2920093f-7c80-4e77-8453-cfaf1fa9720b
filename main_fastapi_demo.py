"""
FastAPI application converted from Flask - Demo Version.
This version works without the business logic modules for demonstration.
"""
import os
import sys
import logging
import time
import asyncio
from typing import Optional, Dict, Any, List
from pathlib import Path
from contextlib import asynccontextmanager

from fastapi import (
    FastAPI, 
    Depends, 
    HTTPException, 
    Request, 
    Form, 
    File, 
    UploadFile,
    Query,
    status
)
from fastapi.responses import (
    JSONResponse, 
    FileResponse, 
    HTMLResponse,
    PlainTextResponse
)
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from ansi2html import Ansi2HTMLConverter

# Import FastAPI modules
from models import (
    ProcessResponse, 
    BusyResponse, 
    LogResponse,
    VideoDataResponse,
    AppConfig
)
from dependencies import (
    get_iap_authentication,
    get_process_management,
    get_app_config,
    clear_process_config,
    update_process_config,
    reset_process_id,
    get_current_process_id,
    process_status
)
from exception_handlers import (
    general_exception_handler,
    http_exception_handler,
    validation_exception_handler
)
from middleware import LoggingMiddleware, setup_logging

# Mock business logic functions for demo
def mock_business_function(request, root_path):
    """Mock function that simulates business logic processing."""
    import tempfile
    import json
    
    # Create a temporary file with some demo data
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        demo_data = {
            "status": "success",
            "message": "Demo processing completed",
            "timestamp": time.time(),
            "request_method": str(request.method) if hasattr(request, 'method') else "POST"
        }
        json.dump(demo_data, f, indent=2)
        temp_file = f.name
    
    # Return: file_path, error_message, success_message, error_code
    return temp_file, "", "Demo processing completed successfully", 0

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logging.info("FastAPI application starting up...")
    
    # Create necessary directories
    os.makedirs("templates", exist_ok=True)
    os.makedirs("static", exist_ok=True)
    
    # Create demo templates
    create_demo_templates()
    
    logging.info("FastAPI application started successfully")
    
    yield
    
    # Shutdown
    logging.info("FastAPI application shutting down...")
    reset_process_id()
    logging.info("FastAPI application shutdown complete")

def create_demo_templates():
    """Create demo HTML templates."""
    templates_dir = Path("templates")
    templates_dir.mkdir(exist_ok=True)
    
    # Create index.html
    index_html = """<!DOCTYPE html>
<html>
<head>
    <title>Hypatia Migration Service</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .endpoint { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .method { font-weight: bold; color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Hypatia Migration Service</h1>
        <p>FastAPI-powered migration and processing service</p>
        
        <h2>Available Endpoints</h2>
        <div class="endpoint">
            <span class="method">GET</span> <a href="/docs">/docs</a> - Interactive API Documentation
        </div>
        <div class="endpoint">
            <span class="method">GET</span> <a href="/health">/health</a> - Health Check
        </div>
        <div class="endpoint">
            <span class="method">GET</span> <a href="/logs">/logs</a> - Logs Page
        </div>
        <div class="endpoint">
            <span class="method">GET</span> <a href="/video">/video</a> - Video Page
        </div>
        
        <h2>Migration Endpoints (POST)</h2>
        <p>All migration endpoints require multipart/form-data with 'uuid' and 'file' fields.</p>
        <div class="endpoint">
            <span class="method">POST</span> /m2vm - M2VM Migration
        </div>
        <div class="endpoint">
            <span class="method">POST</span> /aws_export - AWS Export
        </div>
        <div class="endpoint">
            <span class="method">POST</span> /buildsheet - Buildsheet Generation
        </div>
        <!-- Add more endpoints as needed -->
    </div>
</body>
</html>"""
    
    with open(templates_dir / "index.html", "w") as f:
        f.write(index_html)
    
    # Create log.html
    log_html = """<!DOCTYPE html>
<html>
<head>
    <title>Logs - Hypatia Migration Service</title>
    <style>
        body { font-family: monospace; margin: 20px; }
        .log-container { background: #000; color: #0f0; padding: 20px; border-radius: 5px; }
        pre { white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>Application Logs</h1>
    <div class="log-container">
        <pre>FastAPI Application Logs
========================
Application is running successfully.
Check the server logs for detailed information.

To view real-time logs, check the console where the application is running.
        </pre>
    </div>
    <p><a href="/">← Back to Home</a></p>
</body>
</html>"""
    
    with open(templates_dir / "log.html", "w") as f:
        f.write(log_html)
    
    # Create video.html
    video_html = """<!DOCTYPE html>
<html>
<head>
    <title>Video - Hypatia Migration Service</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Video Processing</h1>
        <p>Video processing functionality</p>
        <p><a href="/video/data">View Video Data (JSON)</a></p>
        <p><a href="/">← Back to Home</a></p>
    </div>
</body>
</html>"""
    
    with open(templates_dir / "video.html", "w") as f:
        f.write(video_html)

# Initialize FastAPI app
app = FastAPI(
    title="Hypatia Migration Service",
    description="Production-ready migration and processing service",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Setup logging
setup_logging()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add custom logging middleware
app.add_middleware(LoggingMiddleware)

# Setup templates and static files
templates = Jinja2Templates(directory="templates")

# Mount static files if directory exists
if Path("static").exists():
    app.mount("/static", StaticFiles(directory="static"), name="static")

# Global variables
converter = Ansi2HTMLConverter()
BUSY_RESPONSE = BusyResponse()

# Exception handlers
app.add_exception_handler(Exception, general_exception_handler)
app.add_exception_handler(HTTPException, http_exception_handler)

async def get_log_content(log_type: str) -> str:
    """Get log content from files."""
    try:
        import aiofiles
        
        if log_type == "server":
            if os.path.exists("app.log"):
                async with aiofiles.open("app.log", "r") as f:
                    content = await f.read()
                    return content
            else:
                return "Server log file not found. Application logs are displayed in the console."
        else:
            return "Process logs are integrated with server logs."
    except Exception as e:
        logging.error(f"Error reading log file: {str(e)}")
        return f"Error reading log file: {str(e)}"

async def process_request_async(
    func: callable,
    function_name: str,
    request: Request,
    root_path: str = "."
) -> FileResponse | PlainTextResponse:
    """Process requests asynchronously with error handling."""
    try:
        clear_process_config(function_name, "processing")
        
        # Execute the function
        file, err, msg, err_code = await asyncio.get_event_loop().run_in_executor(
            None, func, request, root_path
        )
        
        update_process_config(
            file_path=file,
            error_m2vm=err,
            erc=err_code,
            msg=msg,
            status="Completed"
        )
        
        logging.info(f"Process completed: {get_current_process_id()}")
        reset_process_id()
        
        if err_code in [1, 2]:
            return PlainTextResponse(
                content=f"{msg}\n{err}",
                status_code=status.HTTP_205_RESET_CONTENT
            )
        else:
            return FileResponse(
                path=file,
                filename=os.path.basename(file),
                media_type='application/octet-stream'
            )
            
    except Exception as e:
        logging.error(f"Error in process_request_async: {str(e)}", exc_info=True)
        reset_process_id()
        update_process_config(
            msg="Internal error!",
            erc=0,
            status="Error"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "hypatia-migration-service", "version": "1.0.0"}

# Process status endpoint
@app.get("/processd/{pid}", response_model=ProcessResponse)
async def get_process_status(
    pid: str,
    auth: str = Depends(get_iap_authentication)
) -> ProcessResponse:
    """Get process status and results."""
    global BUSY_RESPONSE
    
    current_pid = get_current_process_id()
    
    if not current_pid:
        for _ in range(3):
            if current_pid:
                break
            await asyncio.sleep(1)
        else:
            return BUSY_RESPONSE
    
    if pid != current_pid:
        return BUSY_RESPONSE
    
    while pid == get_current_process_id():
        await asyncio.sleep(1)
    
    logging.info(f"Process status requested for: {pid}")
    
    config = process_status.config
    return ProcessResponse(
        msg=config.msg,
        erc=config.erc,
        cmd=config.gcloudcmd,
        path=config.file_path
    )

# Demo migration endpoints
@app.post("/m2vm")
async def m2vm_endpoint(
    request: Request,
    uuid: str = Form(...),
    file: UploadFile = File(...),
    process_mgmt: Any = Depends(get_process_management)
):
    """M2VM migration endpoint (demo)."""
    return await process_request_async(mock_business_function, "m2vm", request)

@app.post("/aws_export")
async def aws_export_endpoint(
    request: Request,
    uuid: str = Form(...),
    file: UploadFile = File(...),
    process_mgmt: Any = Depends(get_process_management)
):
    """AWS export endpoint (demo)."""
    return await process_request_async(mock_business_function, "aws_export", request)

@app.post("/buildsheet")
async def buildsheet_endpoint(
    request: Request,
    uuid: str = Form(...),
    file: UploadFile = File(...),
    process_mgmt: Any = Depends(get_process_management)
):
    """Buildsheet generation endpoint (demo)."""
    return await process_request_async(mock_business_function, "buildsheet", request)

# File download endpoint
@app.get("/dfile")
async def download_file(
    path: str = Query(..., description="File path to download"),
    auth: str = Depends(get_iap_authentication)
):
    """Download file endpoint."""
    if not path or not os.path.exists(path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Error: Invalid file path or file not found."
        )

    return FileResponse(
        path=path,
        filename=os.path.basename(path),
        media_type='application/octet-stream'
    )

# Template rendering endpoints
@app.get("/logs", response_class=HTMLResponse)
async def logs_page(
    request: Request,
    auth: str = Depends(get_iap_authentication)
):
    """Logs page endpoint."""
    return templates.TemplateResponse("log.html", {"request": request})

@app.get("/video", response_class=HTMLResponse)
async def video_page(
    request: Request,
    auth: str = Depends(get_iap_authentication)
):
    """Video page endpoint."""
    return templates.TemplateResponse("video.html", {"request": request})

@app.get("/video/data", response_model=VideoDataResponse)
async def video_data(
    auth: str = Depends(get_iap_authentication)
):
    """Video data endpoint (demo)."""
    demo_data = [
        {"id": 1, "title": "Demo Video 1", "duration": "10:30"},
        {"id": 2, "title": "Demo Video 2", "duration": "15:45"},
        {"id": 3, "title": "Demo Video 3", "duration": "8:20"}
    ]
    return VideoDataResponse(data=demo_data)

@app.get("/", response_class=HTMLResponse)
async def home(
    request: Request,
    auth: str = Depends(get_iap_authentication)
):
    """Home page endpoint."""
    return templates.TemplateResponse("index.html", {"request": request})

if __name__ == '__main__':
    import uvicorn

    # Get environment configuration
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', 8080))
    debug = os.getenv('ENVIRONMENT') == 'development'

    print(f"Starting FastAPI Demo Application on {host}:{port}")
    print(f"Debug mode: {debug}")
    print(f"Environment: {os.getenv('ENVIRONMENT', 'production')}")
    print(f"API Documentation: http://{host}:{port}/docs")

    # Run the application
    uvicorn.run(
        "main_fastapi_demo:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info" if not debug else "debug",
        access_log=True
    )
