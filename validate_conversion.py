#!/usr/bin/env python3
"""
Validation script for the Flask to FastAPI conversion.
"""
import os
import sys
from pathlib import Path

def validate_files():
    """Validate that all required files exist."""
    print("🔍 Validating Flask to FastAPI conversion...")
    
    required_files = [
        "main.py",              # Original Flask app
        "main_fastapi.py",      # New FastAPI app
        "main_fastapi_demo.py", # Demo FastAPI app
        "models.py",            # Pydantic models
        "dependencies.py",      # FastAPI dependencies
        "exception_handlers.py", # Exception handlers
        "middleware.py",        # Custom middleware
        "run_fastapi.py",       # Startup script
        "pyproject.toml",       # Dependencies
        "FASTAPI_CONVERSION.md" # Documentation
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
        else:
            print(f"   ✅ {file}")
    
    if missing_files:
        print(f"\n❌ Missing files: {missing_files}")
        return False
    
    print("\n✅ All required files present!")
    return True

def validate_dependencies():
    """Validate that dependencies are properly configured."""
    print("\n🔍 Validating dependencies...")
    
    try:
        with open("pyproject.toml", "r") as f:
            content = f.read()
        
        required_deps = [
            "fastapi",
            "uvicorn", 
            "python-multipart",
            "jinja2",
            "aiofiles",
            "ansi2html"
        ]
        
        missing_deps = []
        for dep in required_deps:
            if dep not in content:
                missing_deps.append(dep)
            else:
                print(f"   ✅ {dep}")
        
        if missing_deps:
            print(f"\n❌ Missing dependencies: {missing_deps}")
            return False
        
        print("\n✅ All required dependencies configured!")
        return True
        
    except Exception as e:
        print(f"❌ Error reading pyproject.toml: {e}")
        return False

def validate_code_structure():
    """Validate the code structure and imports."""
    print("\n🔍 Validating code structure...")
    
    try:
        # Test basic imports
        print("   Testing basic imports...")
        from models import ProcessResponse, BusyResponse
        from dependencies import get_current_process_id
        from middleware import setup_logging
        print("   ✅ Core modules import successfully")
        
        # Test model creation
        print("   Testing Pydantic models...")
        response = ProcessResponse(msg="test", erc=0)
        busy = BusyResponse()
        print("   ✅ Pydantic models work correctly")
        
        # Test process management
        print("   Testing process management...")
        pid = get_current_process_id()
        print(f"   ✅ Process management works (current PID: {pid})")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error in code structure validation: {e}")
        return False

def compare_endpoints():
    """Compare Flask and FastAPI endpoints."""
    print("\n🔍 Comparing Flask vs FastAPI endpoints...")
    
    try:
        # Extract Flask routes from main.py
        with open("main.py", "r") as f:
            flask_content = f.read()
        
        flask_routes = []
        for line in flask_content.split('\n'):
            if '@app.route(' in line:
                # Extract route path
                start = line.find("'") + 1
                end = line.find("'", start)
                if start > 0 and end > start:
                    route = line[start:end]
                    flask_routes.append(route)
        
        print(f"   Found {len(flask_routes)} Flask routes:")
        for route in sorted(flask_routes):
            print(f"     • {route}")
        
        # Check FastAPI demo routes
        with open("main_fastapi_demo.py", "r") as f:
            fastapi_content = f.read()
        
        fastapi_routes = []
        for line in fastapi_content.split('\n'):
            if '@app.' in line and '(' in line:
                # Extract route path
                if '"' in line:
                    start = line.find('"') + 1
                    end = line.find('"', start)
                    if start > 0 and end > start:
                        route = line[start:end]
                        fastapi_routes.append(route)
        
        print(f"\n   Found {len(fastapi_routes)} FastAPI routes:")
        for route in sorted(set(fastapi_routes)):
            print(f"     • {route}")
        
        # Check coverage
        flask_set = set(flask_routes)
        fastapi_set = set(fastapi_routes)
        
        covered = flask_set.intersection(fastapi_set)
        missing = flask_set - fastapi_set
        
        print(f"\n   ✅ Covered routes: {len(covered)}/{len(flask_routes)}")
        if missing:
            print(f"   ⚠️  Missing routes: {missing}")
        
        return len(missing) == 0
        
    except Exception as e:
        print(f"   ❌ Error comparing endpoints: {e}")
        return False

def generate_summary():
    """Generate a summary of the conversion."""
    print("\n" + "="*60)
    print("📋 FLASK TO FASTAPI CONVERSION SUMMARY")
    print("="*60)
    
    print("\n🎯 CONVERSION COMPLETED SUCCESSFULLY!")
    
    print("\n📁 Files Created:")
    print("   • main_fastapi.py - Production FastAPI application")
    print("   • main_fastapi_demo.py - Demo FastAPI application")
    print("   • models.py - Pydantic request/response models")
    print("   • dependencies.py - Authentication & process management")
    print("   • exception_handlers.py - Error handling")
    print("   • middleware.py - Custom logging middleware")
    print("   • run_fastapi.py - Application startup script")
    
    print("\n🔧 Key Improvements:")
    print("   • Async/await support for better performance")
    print("   • Type safety with Pydantic models")
    print("   • Automatic API documentation")
    print("   • Modern dependency injection")
    print("   • Structured error handling")
    print("   • Production-ready configuration")
    
    print("\n🚀 Next Steps:")
    print("   1. Copy your business logic modules to this directory")
    print("   2. Create templates/ directory with your HTML templates")
    print("   3. Create static/ directory for static files")
    print("   4. Test with: python main_fastapi_demo.py")
    print("   5. Deploy with: python run_fastapi.py")
    print("   6. Access API docs at: http://localhost:8080/docs")
    
    print("\n📚 Documentation:")
    print("   • See FASTAPI_CONVERSION.md for detailed information")
    print("   • FastAPI docs: https://fastapi.tiangolo.com/")
    
    print("\n" + "="*60)

def main():
    """Main validation function."""
    print("🔄 Flask to FastAPI Conversion Validator")
    print("=" * 50)
    
    success = True
    
    # Run all validations
    success &= validate_files()
    success &= validate_dependencies()
    success &= validate_code_structure()
    success &= compare_endpoints()
    
    if success:
        generate_summary()
        print("\n🎉 VALIDATION SUCCESSFUL! Conversion is complete and ready to use.")
        return 0
    else:
        print("\n❌ VALIDATION FAILED! Please check the errors above.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
