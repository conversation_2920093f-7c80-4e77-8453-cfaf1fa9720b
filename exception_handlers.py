"""
FastAPI exception handlers.
"""
import logging
from fastapi import Request, HTTPException, status
from fastapi.responses import JSO<PERSON>esponse, PlainTextResponse
from dependencies import reset_process_id, update_process_config


async def general_exception_handler(request: Request, exc: Exception) -> PlainTextResponse:
    """
    Global exception handler for unhandled exceptions.
    
    Args:
        request: FastAPI request object
        exc: The exception that occurred
        
    Returns:
        Plain text response with error details
    """
    logging.error(f"Final Error: {str(exc)}", exc_info=True)
    reset_process_id()
    update_process_config(
        msg="Internal error!",
        erc=0,
        status="Error"
    )
    
    return PlainTextResponse(
        content="Internal Server Error",
        status_code=status.HTTP_205_RESET_CONTENT
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """
    Handler for HTTP exceptions.
    
    Args:
        request: FastAPI request object
        exc: HTTP exception
        
    Returns:
        JSON response with error details
    """
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail}
    )


async def validation_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    Handler for validation exceptions.
    
    Args:
        request: FastAPI request object
        exc: Validation exception
        
    Returns:
        JSON response with validation error details
    """
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"detail": "Validation error", "errors": str(exc)}
    )
