import os
import sys
import logging
import time
from functools import wraps
from flask import Flask, jsonify, request, send_file, render_template
from flask_cors import CORS
from ansi2html import Ansi2HTMLConverter

# Import internal modules
from inventory.inventory import Inventory
from kubernetes_inventory.kubernetes import Kubernetes
from migration.buildsheet import buildsheet1
from migration.buildvms import gcloudcmds1
from migration.json_to_excel import json_to_exl
from migration.m2vm import handle_m2vm_request
from migration.move_vali import move_vali
from migration.vmware_excel import vmwarem2vm
from migration.gta_m2vm import gta_m2vm1
from migration.aws_bill import process_aws_bill
from compliance.process import Compliance
from stratozone.stratozone import Stratozone
from glide.init import glide
from video.excel import create_list_of_dictionaries_from_csv

# Flask App Initialization
app = Flask(__name__)
CORS(app)
app.secret_key = os.getenv('SECRET_KEY', 'default_secret_key')
app.config['MAX_CONTENT_LENGTH'] = 256 * 1024 * 1024
app.config['pids'] = []
converter = Ansi2HTMLConverter()
PROCESS_ID = None
BUSY_RESPONSE = {"msg": "App is busy, please wait a few minutes", "erc": 0}


# Remove static file logs using a filter
class NoStaticFilter(logging.Filter):
    def filter(self, record):
        return "GET /static/" not in record.getMessage()


# Logging Configuration
logging.basicConfig(
    filename="app.log",
    level=logging.INFO,
    format="[%(asctime)s] %(levelname)s: %(message)s"
)
# Get Flask's built-in request logger
werkzeug_logger = logging.getLogger("werkzeug")
werkzeug_logger.addFilter(NoStaticFilter())


def get_log_content(log_type):
    try:
        if log_type == "server":
            with open("app.log", "r") as f:
                return f.read()
        else:
            with open("templates/log.html", "r") as f:
                return f.read()
            # return converter.convert(f.read())
    except FileNotFoundError:
        return "Log file not found!"
    except Exception as e:
        logging.error(f"Error reading log file: {str(e)}")
        return f"Error reading log file: {str(e)}"


def errors(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        global PROCESS_ID, BUSY_RESPONSE
        try:
            result = func(*args, **kwargs)
        except Exception as e:
            logging.error(f"Final Error: {str(e)}", exc_info=True)
            PROCESS_ID = None
            BUSY_RESPONSE = {"msg": "Internal error!", "erc": 0}
            return '', 205
        finally:
            app.config['status'] = "Completed"
        return result
    return wrapper


def clear_config(function, status):
    if function not in ['inventory', 'kube', 'stratozone', 'compliance', 'glide', '']:
        time.sleep(3)
    # time.sleep(10)
    app.config.update({
        'gcloudcmd': "",
        'file_path': "",
        'error_m2vm': "",
        'erc': 1,
        'status': status,
        'function': function,
        'msg': ""
    })


@errors
def process_request(func, function_name):
    global PROCESS_ID
    clear_config(function_name, "processing")
    file, err, msg, err_code = func(request, app.root_path)
    app.config.update({'file_path': file, 'error_m2vm': err, 'erc': err_code, 'msg': msg, 'status': "Completed"})
    logging.info(PROCESS_ID)
    PROCESS_ID = None
    return (msg + "\n" + err, 205) if err_code in [1, 2] else send_file(file, as_attachment=True)


@app.route('/processd/<pid>', methods=['GET'])  # route to return error message and code for all functions
def processd(pid):
    global PROCESS_ID, BUSY_RESPONSE
    BUSY_RESPONSE = {"msg": "App is busy, please wait a few minutes", "erc": 0}
    # Wait for process to complete
    if not any(PROCESS_ID or time.sleep(1) for _ in range(3)):
        return jsonify(BUSY_RESPONSE)
    # print(pid, PROCESS_ID)
    if pid != PROCESS_ID:
        return jsonify(BUSY_RESPONSE)

    while pid == PROCESS_ID:
        time.sleep(1)  # Send update every second
    logging.info(PROCESS_ID)

    data = {"msg": app.config['msg'],
            "erc": app.config['erc'],
            "cmd": app.config['gcloudcmd'],
            "path": app.config['file_path']
            }
    return jsonify(data)


@app.before_request
def authorize_request():
    global PROCESS_ID
    if not mode:
        jwt_assertion = request.headers.get('X-Goog-Iap-Jwt-Assertion')
        if not jwt_assertion:
            return 'You are not authorized by IAP.', 401

    if request.method == 'POST' and "multipart/form-data" in request.content_type:
        pid = request.form.get('uuid')
        # print(pid)
        if PROCESS_ID:
            return '', 205
        else:
            PROCESS_ID = pid


@app.route('/move_val', methods=['POST'])
def move_val_endpoint():
    return process_request(move_vali, "move_val")


@app.route('/m2vm', methods=['POST'])
def m2vm_endpoint():
    return process_request(handle_m2vm_request, "handle_m2vm_request")


@app.route('/gta_m2vm', methods=['POST'])
def gta_m2vm_endpoint():
    return process_request(gta_m2vm1, "gta_m2vm")


@app.route('/vmware_m2vm', methods=['POST'])
def vmware_m2vm_endpoint():
    return process_request(vmwarem2vm, "vmware_m2vm")


@app.route('/aws_export', methods=['POST'])
def aws_export_endpoint():
    return process_request(process_aws_bill, "process_aws_bill")


@app.route('/buildsheet', methods=['POST'])
def buildsheet_endpoint():
    return process_request(buildsheet1, "buildsheet")


@app.route('/gcloudcmd', methods=['POST'])
def gcloudcmd_endpoint():
    return process_request(gcloudcmds1, "gcloudcmd")


@app.route('/json_to_xl', methods=['POST'])
def json_to_xl_endpoint():
    return process_request(json_to_exl, "json_to_xl")


@app.route('/compliance', methods=['POST'])
def compliance_endpoint():
    return process_request(Compliance, "compliance")


@app.route('/stratozone', methods=['POST'])
def stratozone_endpoint():
    return process_request(Stratozone, "stratozone")


@app.route('/inventory', methods=['POST'])
def inventory_endpoint():
    return process_request(Inventory, "inventory")


@app.route('/glide', methods=['POST'])
def glide_endpoint():
    return process_request(glide, "glide")


@app.route('/kube', methods=['POST'])
def kube_endpoint():
    return process_request(Kubernetes, "kube")


@app.route('/dfile', methods=['GET'])
def download_file():
    file_path = request.args.get('path')
    return send_file(file_path, as_attachment=True) if file_path and os.path.exists(
        file_path) else "Error: Invalid file path or file not found."


@app.route('/logs', methods=['GET'])
def logs_page():
    return render_template('log.html')


@app.route('/logging')
def log_view():
    return render_template('logging.html', server_content=get_log_content("server"), process_content=get_log_content("process"))


@app.route('/video', methods=['GET'])
def video_page():
    return render_template('video.html')


@app.route('/video/data', methods=['GET'])
def video_data():
    return jsonify(create_list_of_dictionaries_from_csv('video/data1.csv'))


@app.route('/', methods=['GET'])
def home():
    return render_template('index.html')


if __name__ == '__main__':
    mode = False
    try:
        if os.getenv('ENVIRONMENT') == 'development':
            mode = True
    except Exception as e:
        mode = False
    app.run(host='0.0.0.0', port=int(os.getenv('PORT', 8080)), debug=mode)
