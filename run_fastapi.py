#!/usr/bin/env python3
"""
Startup script for the FastAPI application.
"""
import os
import uvicorn

if __name__ == '__main__':
    # Get environment configuration
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', 8080))
    debug = os.getenv('ENVIRONMENT') == 'development'
    
    print(f"Starting FastAPI application on {host}:{port}")
    print(f"Debug mode: {debug}")
    print(f"Environment: {os.getenv('ENVIRONMENT', 'production')}")
    
    # Run the application
    uvicorn.run(
        "main_fastapi:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info" if not debug else "debug",
        access_log=True,
        workers=1 if debug else 4  # Use multiple workers in production
    )
